'use client';

import { KEYWORDS_LOADING_KEYS, LOADING_SCOPES } from '@/constants';
import { KeywordWithDetail } from '@/models';
import {
	createContext,
	useCallback,
	useContext,
	useEffect,
	useMemo,
	useRef,
	useState,
} from 'react';
import { useLoadingError, useScopedLoading } from './loading-context';
import { useAuth } from './auth-context';

type KeywordsContextType = {
	keywords: KeywordWithDetail[];
	isLoading: boolean;
	error: Error | null;
	selectedKeywords: string[];
	setSelectedKeywords: (ids: string[]) => void;
	fetchKeywords: () => Promise<void>;
	getKeyword: (id: string) => Promise<KeywordWithDetail | null>;
	searchKeywords: (term: string) => Promise<void>;
	createKeyword: (name: string) => Promise<KeywordWithDetail | null>;
	updateKeyword: (id: string, name: string) => Promise<KeywordWithDetail | null>;
	deleteKeyword: (id: string) => Promise<void>;
	getLoadingState: (key: string) => boolean;
};

const KeywordsContext = createContext<KeywordsContextType | undefined>(undefined);

export function KeywordsProvider({ children }: { children: React.ReactNode }) {
	const [keywords, setKeywords] = useState<KeywordWithDetail[]>([]);
	const [error, setError] = useState<Error | null>(null);
	const [selectedKeywords, setSelectedKeywords] = useState<string[]>([]);
	const { getLoading } = useScopedLoading(LOADING_SCOPES.KEYWORDS);
	const loadingErrorHelper = useLoadingError(LOADING_SCOPES.KEYWORDS);
	const { user } = useAuth();

	// Note: Direct server API calls only - no IndexedDB caching

	// Load keywords from server on mount
	useEffect(() => {
		fetchKeywords();
	}, [fetchKeywords]);

	// Computed loading state - only show loading for initial fetch
	const isLoading = getLoading(KEYWORDS_LOADING_KEYS.FETCH_KEYWORDS);

	const fetchKeywords = useCallback(async () => {
		const { start, end } = loadingErrorHelper(
			() => {},
			setError,
			KEYWORDS_LOADING_KEYS.FETCH_KEYWORDS
		);
		start();
		try {
			// Fetch directly from server
			const response = await fetch('/api/keywords');
			if (!response.ok) {
				throw new Error('Failed to fetch keywords from server');
			}
			const result: KeywordWithDetail[] = await response.json();
			setKeywords(result);
			end();
		} catch (err) {
			const error = err instanceof Error ? err : new Error('Failed to fetch keywords');
			end(error);
		}
	}, [loadingErrorHelper]);

	const getKeyword = useCallback(
		async (id: string): Promise<KeywordWithDetail | null> => {
			// First try to get from current state
			const localKeyword = keywords.find((k) => k.id === id);
			if (localKeyword) {
				return localKeyword;
			}

			// If not found in state, fetch from server
			try {
				const response = await fetch(`/api/keywords/${id}`);
				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || 'Failed to get keyword');
				}
				const result = await response.json();
				return result;
			} catch (err) {
				console.error('Failed to fetch keyword from server:', err);
				return null;
			}
		},
		[keywords]
	);

	const searchKeywords = useCallback(
		async (term: string) => {
			// Search in current state (or fetch from server if needed)
			if (!term.trim()) {
				// If empty search, reload all keywords
				await fetchKeywords();
				return;
			}

			// Filter current keywords by search term
			const filtered = keywords.filter((keyword) =>
				keyword.content.toLowerCase().includes(term.toLowerCase())
			);
			setKeywords(filtered);
		},
		[keywords, fetchKeywords]
	);

	const createKeyword = useCallback(
		async (name: string) => {
			try {
				// Check if user is authenticated
				if (!user?.id) {
					throw new Error('User must be authenticated to create keywords');
				}

				// Create directly on server
				const response = await fetch('/api/keywords', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({ name: name.trim() }),
				});

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || 'Failed to create keyword');
				}

				const serverKeyword: KeywordWithDetail = await response.json();

				// Update local state with new keyword
				setKeywords((prev) => [...prev, serverKeyword]);

				return serverKeyword;
			} catch (err) {
				const error = err instanceof Error ? err : new Error('Failed to create keyword');
				setError(error);
				return null;
			}
		},
		[user?.id]
	);

	const updateKeyword = useCallback(async (id: string, name: string) => {
		try {
			// Update directly on server
			const response = await fetch(`/api/keywords/${id}`, {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ name: name.trim() }),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to update keyword');
			}

			const updatedKeyword: KeywordWithDetail = await response.json();

			// Update local state
			setKeywords((prev) => prev.map((k) => (k.id === id ? updatedKeyword : k)));

			return updatedKeyword;
		} catch (err) {
			const error = err instanceof Error ? err : new Error('Failed to update keyword');
			setError(error);
			return null;
		}
	}, []);

	const deleteKeyword = useCallback(async (id: string) => {
		try {
			// Delete directly on server
			const response = await fetch(`/api/keywords/${id}`, {
				method: 'DELETE',
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to delete keyword');
			}

			// Update local state - remove the deleted keyword
			setKeywords((prev) => prev.filter((k) => k.id !== id));

			// Also remove from selected keywords
			setSelectedKeywords((prev) => prev.filter((keywordId) => keywordId !== id));
		} catch (err) {
			const error = err instanceof Error ? err : new Error('Failed to delete keyword');
			setError(error);
		}
	}, []);

	// Simple setSelectedKeywords - no storage needed
	const setSelectedKeywordsWithStorage = useCallback((ids: string[]) => {
		setSelectedKeywords(ids);
	}, []);

	// Note: Sync status tracking removed since we're using direct API calls

	const value = useMemo(
		() => ({
			keywords,
			isLoading,
			error,
			selectedKeywords,
			setSelectedKeywords: setSelectedKeywordsWithStorage,
			fetchKeywords,
			getKeyword,
			searchKeywords,
			createKeyword,
			updateKeyword,
			deleteKeyword,
			getLoadingState: getLoading,
		}),
		[
			keywords,
			isLoading,
			error,
			selectedKeywords,
			setSelectedKeywordsWithStorage,
			fetchKeywords,
			getKeyword,
			searchKeywords,
			createKeyword,
			updateKeyword,
			deleteKeyword,
			getLoading,
		]
	);

	return <KeywordsContext.Provider value={value}>{children}</KeywordsContext.Provider>;
}

export function useKeywordsContext() {
	const context = useContext(KeywordsContext);
	if (context === undefined)
		throw new Error('useKeywordsContext must be used within a KeywordsProvider');
	return context;
}
