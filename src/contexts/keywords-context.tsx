'use client';

import { KEYWORDS_LOADING_KEYS, LOADING_SCOPES } from '@/constants';
import { KeywordWithDetail } from '@/models';
import { keywordStorage } from '@/lib/keyword-storage';
import { useKeywordSync } from '@/hooks/use-keyword-sync';
import {
	createContext,
	useCallback,
	useContext,
	useEffect,
	useMemo,
	useRef,
	useState,
} from 'react';
import { useLoadingError, useScopedLoading } from './loading-context';

type KeywordsContextType = {
	keywords: KeywordWithDetail[];
	isLoading: boolean;
	error: Error | null;
	selectedKeywords: string[];
	setSelectedKeywords: (ids: string[]) => void;
	fetchKeywords: () => Promise<void>;
	getKeyword: (id: string) => Promise<KeywordWithDetail | null>;
	searchKeywords: (term: string) => Promise<void>;
	createKeyword: (name: string) => Promise<KeywordWithDetail | null>;
	updateKeyword: (id: string, name: string) => Promise<KeywordWithDetail | null>;
	deleteKeyword: (id: string) => Promise<void>;
	getLoadingState: (key: string) => boolean;
	syncStatus: {
		pendingActions: number;
		isSync: boolean;
		hasUnsyncedChanges: boolean;
		showSyncSuccess: boolean;
	};
	syncNow: () => Promise<void>;
};

const KeywordsContext = createContext<KeywordsContextType | undefined>(undefined);

export function KeywordsProvider({ children }: { children: React.ReactNode }) {
	const [keywords, setKeywords] = useState<KeywordWithDetail[]>([]);
	const [error, setError] = useState<Error | null>(null);
	const [selectedKeywords, setSelectedKeywords] = useState<string[]>([]);
	const [syncStatus, setSyncStatus] = useState({
		pendingActions: 0,
		isSync: false,
		hasUnsyncedChanges: false,
		showSyncSuccess: false,
	});
	const { getLoading } = useScopedLoading(LOADING_SCOPES.KEYWORDS);
	const loadingErrorHelper = useLoadingError(LOADING_SCOPES.KEYWORDS);

	// Initialize background sync
	const { syncNow, getSyncStatus } = useKeywordSync({
		onSyncSuccess: (action) => {
			console.log('Keyword sync success:', action);
		},
		onSyncError: (action, error) => {
			console.error('Keyword sync error:', action, error);
			setError(error);
		},
	});

	// Load keywords from IndexedDB on mount
	useEffect(() => {
		const loadKeywords = async () => {
			try {
				const localKeywords = await keywordStorage.getKeywords();
				const localSelectedKeywords = await keywordStorage.getSelectedKeywords();
				setKeywords(localKeywords);
				setSelectedKeywords(localSelectedKeywords);
			} catch (error) {
				console.error('Failed to load keywords from IndexedDB:', error);
				setError(error instanceof Error ? error : new Error('Failed to load keywords'));
			}
		};
		loadKeywords();
	}, []);

	// Computed loading state - only show loading for initial fetch
	const isLoading = getLoading(KEYWORDS_LOADING_KEYS.FETCH_KEYWORDS);

	const fetchKeywords = useCallback(async () => {
		const { start, end } = loadingErrorHelper(
			() => {},
			setError,
			KEYWORDS_LOADING_KEYS.FETCH_KEYWORDS
		);
		start();
		try {
			// Fetch from local storage (which includes initial server fetch)
			const result = await keywordStorage.getKeywords();
			setKeywords(result);
			end();
		} catch (err) {
			const error = err instanceof Error ? err : new Error('Failed to fetch keywords');
			end(error);
		}
	}, [loadingErrorHelper]);

	const getKeyword = useCallback(async (id: string): Promise<KeywordWithDetail | null> => {
		// First try to get from IndexedDB
		const localKeywords = await keywordStorage.getKeywords();
		const localKeyword = localKeywords.find((k) => k.id === id);
		if (localKeyword) {
			return localKeyword;
		}

		// If not found locally, fetch from server
		try {
			const response = await fetch(`/api/keywords/${id}`);
			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to get keyword');
			}
			const result = await response.json();
			return result;
		} catch (err) {
			console.error('Failed to fetch keyword from server:', err);
			return null;
		}
	}, []);

	const searchKeywords = useCallback(async (term: string) => {
		// Search in IndexedDB
		const localKeywords = await keywordStorage.getKeywords();
		const filtered = localKeywords.filter((keyword) =>
			keyword.content.toLowerCase().includes(term.toLowerCase())
		);
		setKeywords(filtered);
	}, []);

	const createKeyword = useCallback(async (name: string) => {
		try {
			// Create keyword directly on server first
			const response = await fetch('/api/keywords', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ name: name.trim() }),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to create keyword');
			}

			const newKeyword: KeywordWithDetail = await response.json();

			// Add to local storage
			await keywordStorage.addKeywordLocally(newKeyword);
			const updatedKeywords = await keywordStorage.getKeywords();
			setKeywords(updatedKeywords);

			return newKeyword;
		} catch (err) {
			const error = err instanceof Error ? err : new Error('Failed to create keyword');
			setError(error);
			return null;
		}
	}, []);

	const updateKeyword = useCallback(async (id: string, name: string) => {
		try {
			// Update on server first
			const response = await fetch(`/api/keywords/${id}`, {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ name: name.trim() }),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to update keyword');
			}

			const updatedKeyword: KeywordWithDetail = await response.json();

			// Update local storage
			await keywordStorage.updateKeywordLocally(id, {
				content: updatedKeyword.content,
			});
			const updatedKeywords = await keywordStorage.getKeywords();
			setKeywords(updatedKeywords);

			return updatedKeyword;
		} catch (err) {
			const error = err instanceof Error ? err : new Error('Failed to update keyword');
			setError(error);
			return null;
		}
	}, []);

	const deleteKeyword = useCallback(async (id: string) => {
		try {
			// Delete on server first
			const response = await fetch(`/api/keywords/${id}`, {
				method: 'DELETE',
			});

			if (!response.ok && response.status !== 404) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to delete keyword');
			}

			// Delete locally
			await keywordStorage.deleteKeywordLocally(id);
			const updatedKeywords = await keywordStorage.getKeywords();
			const updatedSelected = await keywordStorage.getSelectedKeywords();
			setKeywords(updatedKeywords);
			setSelectedKeywords(updatedSelected);
		} catch (err) {
			const error = err instanceof Error ? err : new Error('Failed to delete keyword');
			setError(error);
		}
	}, []);

	// Custom setSelectedKeywords that also saves to IndexedDB
	const setSelectedKeywordsWithStorage = useCallback(async (ids: string[]) => {
		setSelectedKeywords(ids);
		await keywordStorage.saveSelectedKeywords(ids);
	}, []);

	const isFirstLoad = useRef(true);
	useEffect(() => {
		const checkAndFetch = async () => {
			if (isFirstLoad.current) {
				isFirstLoad.current = false;
				// Only fetch from server if no local data exists
				const localKeywords = await keywordStorage.getKeywords();
				if (localKeywords.length === 0) {
					fetchKeywords();
				}
			}
		};
		checkAndFetch();
	}, [fetchKeywords]);

	// Update sync status periodically
	useEffect(() => {
		const updateSyncStatus = async () => {
			const status = await getSyncStatus();
			setSyncStatus(status);
		};

		// Initial update
		updateSyncStatus();

		// Update every 2 seconds
		const interval = setInterval(updateSyncStatus, 2000);

		return () => clearInterval(interval);
	}, [getSyncStatus]);

	const value = useMemo(
		() => ({
			keywords,
			isLoading,
			error,
			selectedKeywords,
			setSelectedKeywords: setSelectedKeywordsWithStorage,
			fetchKeywords,
			getKeyword,
			searchKeywords,
			createKeyword,
			updateKeyword,
			deleteKeyword,
			getLoadingState: getLoading,
			syncStatus,
			syncNow,
		}),
		[
			keywords,
			isLoading,
			error,
			selectedKeywords,
			setSelectedKeywordsWithStorage,
			fetchKeywords,
			getKeyword,
			searchKeywords,
			createKeyword,
			updateKeyword,
			deleteKeyword,
			getLoading,
			syncStatus,
			syncNow,
		]
	);

	return <KeywordsContext.Provider value={value}>{children}</KeywordsContext.Provider>;
}

export function useKeywordsContext() {
	const context = useContext(KeywordsContext);
	if (context === undefined)
		throw new Error('useKeywordsContext must be used within a KeywordsProvider');
	return context;
}
