'use client';

import { KEYWORDS_LOADING_KEYS, LOADING_SCOPES } from '@/constants';
import { KeywordWithDetail } from '@/models';
import { keywordStorage } from '@/lib/keyword-storage';
import {
	createContext,
	useCallback,
	useContext,
	useEffect,
	useMemo,
	useRef,
	useState,
} from 'react';
import { useLoadingError, useScopedLoading } from './loading-context';
import { useAuth } from './auth-context';

type KeywordsContextType = {
	keywords: KeywordWithDetail[];
	isLoading: boolean;
	error: Error | null;
	selectedKeywords: string[];
	setSelectedKeywords: (ids: string[]) => void;
	fetchKeywords: () => Promise<void>;
	getKeyword: (id: string) => Promise<KeywordWithDetail | null>;
	searchKeywords: (term: string) => Promise<void>;
	createKeyword: (name: string) => Promise<KeywordWithDetail | null>;
	updateKeyword: (id: string, name: string) => Promise<KeywordWithDetail | null>;
	deleteKeyword: (id: string) => Promise<void>;
	getLoadingState: (key: string) => boolean;
	syncStatus: {
		pendingActions: number;
		isSync: boolean;
		hasUnsyncedChanges: boolean;
		showSyncSuccess: boolean;
	};
};

const KeywordsContext = createContext<KeywordsContextType | undefined>(undefined);

export function KeywordsProvider({ children }: { children: React.ReactNode }) {
	const [keywords, setKeywords] = useState<KeywordWithDetail[]>([]);
	const [error, setError] = useState<Error | null>(null);
	const [selectedKeywords, setSelectedKeywords] = useState<string[]>([]);
	const [syncStatus, setSyncStatus] = useState({
		pendingActions: 0,
		isSync: false,
		hasUnsyncedChanges: false,
		showSyncSuccess: false,
	});
	const { getLoading } = useScopedLoading(LOADING_SCOPES.KEYWORDS);
	const loadingErrorHelper = useLoadingError(LOADING_SCOPES.KEYWORDS);
	const { user } = useAuth();

	// Note: Using direct API calls with optimistic updates instead of background sync
	// This prioritizes local changes and overwrites remote state

	// Load keywords from IndexedDB on mount
	useEffect(() => {
		const loadKeywords = async () => {
			try {
				const localKeywords = await keywordStorage.getKeywords();
				const localSelectedKeywords = await keywordStorage.getSelectedKeywords();
				setKeywords(localKeywords);
				setSelectedKeywords(localSelectedKeywords);
			} catch (error) {
				console.error('Failed to load keywords from IndexedDB:', error);
				setError(error instanceof Error ? error : new Error('Failed to load keywords'));
			}
		};
		loadKeywords();
	}, []);

	// Computed loading state - only show loading for initial fetch
	const isLoading = getLoading(KEYWORDS_LOADING_KEYS.FETCH_KEYWORDS);

	const fetchKeywords = useCallback(async () => {
		const { start, end } = loadingErrorHelper(
			() => {},
			setError,
			KEYWORDS_LOADING_KEYS.FETCH_KEYWORDS
		);
		start();
		try {
			// Fetch from local storage (which includes initial server fetch)
			const result = await keywordStorage.getKeywords();
			setKeywords(result);
			end();
		} catch (err) {
			const error = err instanceof Error ? err : new Error('Failed to fetch keywords');
			end(error);
		}
	}, [loadingErrorHelper]);

	const getKeyword = useCallback(async (id: string): Promise<KeywordWithDetail | null> => {
		// First try to get from IndexedDB
		const localKeywords = await keywordStorage.getKeywords();
		const localKeyword = localKeywords.find((k) => k.id === id);
		if (localKeyword) {
			return localKeyword;
		}

		// If not found locally, fetch from server
		try {
			const response = await fetch(`/api/keywords/${id}`);
			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to get keyword');
			}
			const result = await response.json();
			return result;
		} catch (err) {
			console.error('Failed to fetch keyword from server:', err);
			return null;
		}
	}, []);

	const searchKeywords = useCallback(async (term: string) => {
		// Search in IndexedDB
		const localKeywords = await keywordStorage.getKeywords();
		const filtered = localKeywords.filter((keyword) =>
			keyword.content.toLowerCase().includes(term.toLowerCase())
		);
		setKeywords(filtered);
	}, []);

	const createKeyword = useCallback(
		async (name: string) => {
			try {
				// Check if user is authenticated
				if (!user?.id) {
					throw new Error('User must be authenticated to create keywords');
				}

				// Create temporary keyword for immediate UI update (optimistic)
				const tempId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
				const tempKeyword: KeywordWithDetail = {
					id: tempId,
					content: name.trim(),
					user_id: user.id,
				};

				// Add to local storage immediately
				await keywordStorage.addKeywordLocally(tempKeyword);
				const updatedKeywords = await keywordStorage.getKeywords();
				setKeywords(updatedKeywords);

				// Create on server and replace temporary keyword
				try {
					const response = await fetch('/api/keywords', {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json',
						},
						body: JSON.stringify({ name: name.trim() }),
					});

					if (response.ok) {
						const serverKeyword: KeywordWithDetail = await response.json();

						// Replace temporary keyword with server keyword
						await keywordStorage.deleteKeywordLocally(tempId);
						await keywordStorage.addKeywordLocally(serverKeyword);

						// Update selected keywords if temp was selected
						const selectedKeywords = await keywordStorage.getSelectedKeywords();
						if (selectedKeywords.includes(tempId)) {
							const updatedSelected = selectedKeywords.map((id) =>
								id === tempId ? serverKeyword.id : id
							);
							await keywordStorage.saveSelectedKeywords(updatedSelected);
							setSelectedKeywords(updatedSelected);
						}

						// Update UI with final state
						const finalKeywords = await keywordStorage.getKeywords();
						setKeywords(finalKeywords);

						return serverKeyword;
					} else {
						// Server error - keep temporary keyword but log error
						console.warn('Failed to create keyword on server, keeping local copy');
						return tempKeyword;
					}
				} catch (serverErr) {
					// Network error - keep temporary keyword
					console.warn('Network error creating keyword, keeping local copy:', serverErr);
					return tempKeyword;
				}
			} catch (err) {
				const error = err instanceof Error ? err : new Error('Failed to create keyword');
				setError(error);
				return null;
			}
		},
		[user?.id]
	);

	const updateKeyword = useCallback(async (id: string, name: string) => {
		try {
			// Optimistic update: Update locally first (overwrite remote approach)
			await keywordStorage.updateKeywordLocally(id, { content: name.trim() });

			// Update UI state immediately
			const updatedKeywords = await keywordStorage.getKeywords();
			setKeywords(updatedKeywords);

			// Update on server (don't wait for response, fire and forget)
			// Only update real keywords, not temporary ones
			if (!id.startsWith('temp_')) {
				fetch(`/api/keywords/${id}`, {
					method: 'PUT',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({ name: name.trim() }),
				}).catch((err) => {
					console.warn('Failed to update keyword on server:', err);
					// Don't revert local changes - prioritize local state
				});
			}

			// Return the updated keyword from local storage
			const updatedKeyword = updatedKeywords.find((k) => k.id === id);
			return updatedKeyword || null;
		} catch (err) {
			const error = err instanceof Error ? err : new Error('Failed to update keyword');
			setError(error);
			return null;
		}
	}, []);

	const deleteKeyword = useCallback(async (id: string) => {
		try {
			// Optimistic update: Delete locally first (overwrite remote approach)
			await keywordStorage.deleteKeywordLocally(id);

			// Update UI state immediately
			const updatedKeywords = await keywordStorage.getKeywords();
			const updatedSelected = await keywordStorage.getSelectedKeywords();
			setKeywords(updatedKeywords);
			setSelectedKeywords(updatedSelected);

			// Delete on server (don't wait for response, fire and forget)
			// Only delete real keywords, not temporary ones
			if (!id.startsWith('temp_')) {
				fetch(`/api/keywords/${id}`, {
					method: 'DELETE',
				}).catch((err) => {
					console.warn('Failed to delete keyword on server:', err);
					// Don't revert local changes - prioritize local state
				});
			}
		} catch (err) {
			const error = err instanceof Error ? err : new Error('Failed to delete keyword');
			setError(error);

			// Revert local changes on error
			try {
				const allKeywords = await keywordStorage.getKeywords();
				setKeywords(allKeywords);
				const allSelected = await keywordStorage.getSelectedKeywords();
				setSelectedKeywords(allSelected);
			} catch (revertErr) {
				console.error('Failed to revert keyword state:', revertErr);
			}
		}
	}, []);

	// Custom setSelectedKeywords that also saves to IndexedDB
	const setSelectedKeywordsWithStorage = useCallback(async (ids: string[]) => {
		setSelectedKeywords(ids);
		await keywordStorage.saveSelectedKeywords(ids);
	}, []);

	const isFirstLoad = useRef(true);
	useEffect(() => {
		const checkAndFetch = async () => {
			if (isFirstLoad.current) {
				isFirstLoad.current = false;
				// Only fetch from server if no local data exists
				const localKeywords = await keywordStorage.getKeywords();
				if (localKeywords.length === 0) {
					fetchKeywords();
				}
			}
		};
		checkAndFetch();
	}, [fetchKeywords]);

	// Note: Sync status tracking removed since we're using direct API calls

	const value = useMemo(
		() => ({
			keywords,
			isLoading,
			error,
			selectedKeywords,
			setSelectedKeywords: setSelectedKeywordsWithStorage,
			fetchKeywords,
			getKeyword,
			searchKeywords,
			createKeyword,
			updateKeyword,
			deleteKeyword,
			getLoadingState: getLoading,
			syncStatus,
		}),
		[
			keywords,
			isLoading,
			error,
			selectedKeywords,
			setSelectedKeywordsWithStorage,
			fetchKeywords,
			getKeyword,
			searchKeywords,
			createKeyword,
			updateKeyword,
			deleteKeyword,
			getLoading,
			syncStatus,
		]
	);

	return <KeywordsContext.Provider value={value}>{children}</KeywordsContext.Provider>;
}

export function useKeywordsContext() {
	const context = useContext(KeywordsContext);
	if (context === undefined)
		throw new Error('useKeywordsContext must be used within a KeywordsProvider');
	return context;
}
